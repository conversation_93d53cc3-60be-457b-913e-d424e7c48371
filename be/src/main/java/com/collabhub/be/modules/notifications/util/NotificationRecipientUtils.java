package com.collabhub.be.modules.notifications.util;

import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.UnifiedNotificationRecipient;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Production-grade utility class for notification recipient management.
 *
 * <p>This utility class provides factory methods and conversion utilities for working
 * with notification recipients in the unified notification system. It handles the
 * complexity of converting between different data sources and recipient types.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Type-safe conversion from various data sources</li>
 *   <li>Bulk operations for performance optimization</li>
 *   <li>Validation and error handling</li>
 *   <li>Recipient filtering and grouping utilities</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class NotificationRecipientUtils {

    private NotificationRecipientUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // ========================================
    // FACTORY METHODS
    // ========================================

    /**
     * Creates a UnifiedNotificationRecipient from a User entity.
     *
     * @param user the user entity (must not be null)
     * @return UnifiedNotificationRecipient instance for internal user
     * @throws IllegalArgumentException if user is invalid
     */
    public static UnifiedNotificationRecipient fromUser(@NotNull User user) {
        return UnifiedNotificationRecipient.fromUser(user);
    }

    /**
     * Creates a UnifiedNotificationRecipient from a HubParticipant entity.
     *
     * <p>Automatically determines if the participant is internal (has user_id) or
     * external (email-only) and creates the appropriate unified recipient.</p>
     *
     * @param participant the hub participant (must not be null)
     * @return UnifiedNotificationRecipient instance
     * @throws IllegalArgumentException if participant is invalid or internal participants lack user data
     */
    public static UnifiedNotificationRecipient fromHubParticipant(@NotNull HubParticipant participant) {
        if (participant.getUserId() != null) {
            // Internal participant - need to get user data
            throw new IllegalArgumentException("Internal participants require User entity data. Use fromHubParticipantWithUser() instead.");
        } else {
            // External participant
            return UnifiedNotificationRecipient.fromHubParticipant(participant);
        }
    }

    /**
     * Creates a UnifiedNotificationRecipient from a HubParticipant with User data.
     *
     * <p>This method should be used when you have both HubParticipant and User data
     * for internal participants, ensuring complete recipient information.</p>
     *
     * @param participant the hub participant (must not be null)
     * @param user the user entity (must not be null for internal participants)
     * @return UnifiedNotificationRecipient instance
     * @throws IllegalArgumentException if data is inconsistent
     */
    public static UnifiedNotificationRecipient fromHubParticipantWithUser(@NotNull HubParticipant participant,
                                                                          @NotNull User user) {
        return UnifiedNotificationRecipient.fromHubParticipantWithUser(participant, user);
    }

    // ========================================
    // BULK CONVERSION METHODS
    // ========================================

    /**
     * Converts a list of User entities to UnifiedNotificationRecipients.
     *
     * @param users the list of users (must not be null or empty)
     * @return list of UnifiedNotificationRecipient instances for internal users
     * @throws IllegalArgumentException if any user is invalid
     */
    public static List<UnifiedNotificationRecipient> fromUsers(@NotEmpty List<User> users) {
        return users.stream()
                   .map(UnifiedNotificationRecipient::fromUser)
                   .collect(Collectors.toList());
    }

    /**
     * Converts a list of HubParticipant entities to UnifiedNotificationRecipients.
     *
     * <p>This method handles mixed participants (both internal and external) but
     * requires that internal participants have complete user data available.</p>
     *
     * @param participants the list of participants (must not be null or empty)
     * @return list of UnifiedNotificationRecipient instances
     * @throws IllegalArgumentException if any participant is invalid or internal participants lack user data
     */
    public static List<UnifiedNotificationRecipient> fromHubParticipants(@NotEmpty List<HubParticipant> participants) {
        List<UnifiedNotificationRecipient> recipients = new ArrayList<>();

        for (HubParticipant participant : participants) {
            if (participant.getUserId() != null) {
                throw new IllegalArgumentException("Internal participants require User entity data. Use convertHubParticipantsToRecipients() instead.");
            } else {
                recipients.add(UnifiedNotificationRecipient.fromHubParticipant(participant));
            }
        }

        return recipients;
    }

    /**
     * Converts a list of HubParticipant entities to UnifiedNotificationRecipients with User lookup.
     *
     * <p>This method handles mixed participants (both internal and external) and
     * performs bulk User entity loading for internal participants. This is the preferred
     * method for converting hub participants in service classes.</p>
     *
     * @param participants the list of participants (must not be null or empty)
     * @param userRepository the user repository for bulk loading internal users
     * @return list of UnifiedNotificationRecipient instances
     * @throws IllegalArgumentException if any participant is invalid
     */
    public static List<UnifiedNotificationRecipient> convertHubParticipantsToRecipients(
            @NotEmpty List<HubParticipant> participants,
            @NotNull com.collabhub.be.modules.auth.repository.UserRepository userRepository) {

        if (participants.isEmpty()) {
            return List.of();
        }

        List<UnifiedNotificationRecipient> recipients = new ArrayList<>();
        List<Long> internalUserIds = new ArrayList<>();

        // Separate internal and external participants
        for (HubParticipant participant : participants) {
            if (participant.getUserId() != null) {
                // Internal participant - collect user ID for bulk loading
                internalUserIds.add(participant.getUserId());
            } else {
                // External participant - create recipient directly
                recipients.add(UnifiedNotificationRecipient.fromHubParticipant(participant));
            }
        }

        // Bulk load internal users if any
        if (!internalUserIds.isEmpty()) {
            List<org.jooq.generated.tables.pojos.User> users = userRepository.findByIds(internalUserIds);
            recipients.addAll(users.stream()
                                  .map(UnifiedNotificationRecipient::fromUser)
                                  .toList());
        }

        return recipients;
    }

    // ========================================
    // RECIPIENT UTILITY METHODS
    // ========================================

    /**
     * Converts a mixed list of NotificationRecipients to UnifiedNotificationRecipients.
     *
     * @param recipients the mixed recipient list (must not be null)
     * @return list of unified notification recipients
     */
    public static List<UnifiedNotificationRecipient> toUnifiedRecipients(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .map(NotificationRecipientUtils::toUnifiedRecipient)
                        .collect(Collectors.toList());
    }

    /**
     * Converts a single NotificationRecipient to UnifiedNotificationRecipient.
     *
     * @param recipient the recipient to convert (must not be null)
     * @return unified notification recipient
     */
    public static UnifiedNotificationRecipient toUnifiedRecipient(@NotNull NotificationRecipient recipient) {
        if (recipient instanceof UnifiedNotificationRecipient) {
            return (UnifiedNotificationRecipient) recipient;
        }

        // Handle legacy recipient types during transition
        if (recipient.isInternal()) {
            return UnifiedNotificationRecipient.forInternalUser(
                recipient.userId(), recipient.email(), recipient.displayName());
        } else {
            return UnifiedNotificationRecipient.forExternalUser(
                recipient.email(), recipient.displayName());
        }
    }

    /**
     * Extracts user IDs from internal user recipients.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of user IDs from internal recipients
     */
    public static Set<Long> extractUserIds(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(NotificationRecipient::isInternal)
                        .map(NotificationRecipient::userId)
                        .collect(Collectors.toSet());
    }

    /**
     * Extracts email addresses from all recipients.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of email addresses from all recipients
     */
    public static Set<String> extractEmails(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .map(NotificationRecipient::email)
                        .collect(Collectors.toSet());
    }

    /**
     * Extracts email addresses from external user recipients only.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of email addresses from external recipients
     */
    public static Set<String> extractExternalEmails(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(r -> !r.isInternal())
                        .map(NotificationRecipient::email)
                        .collect(Collectors.toSet());
    }

    /**
     * Counts internal and external recipients.
     *
     * @param recipients the recipient list (must not be null)
     * @return array with [internalCount, externalCount]
     */
    public static long[] countRecipientTypes(@NotNull List<NotificationRecipient> recipients) {
        long internalCount = recipients.stream().filter(NotificationRecipient::isInternal).count();
        long externalCount = recipients.size() - internalCount;
        return new long[]{internalCount, externalCount};
    }

    // ========================================
    // VALIDATION AND UTILITY METHODS
    // ========================================

    /**
     * Validates a list of recipients, ensuring all are properly formed.
     *
     * @param recipients the recipient list to validate (must not be null)
     * @throws IllegalArgumentException if any recipient is invalid
     */
    public static void validateRecipients(@NotNull List<NotificationRecipient> recipients) {
        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipient list cannot be empty");
        }
        
        for (int i = 0; i < recipients.size(); i++) {
            NotificationRecipient recipient = recipients.get(i);
            if (recipient == null) {
                throw new IllegalArgumentException("Recipient at index " + i + " is null");
            }
            
            try {
                recipient.validate();
            } catch (Exception e) {
                throw new IllegalArgumentException("Recipient at index " + i + " is invalid: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Removes duplicate recipients from a list based on unique identifiers.
     *
     * @param recipients the recipient list (must not be null)
     * @return deduplicated list maintaining original order
     */
    public static List<NotificationRecipient> deduplicateRecipients(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .collect(Collectors.toMap(
                            NotificationRecipient::getUniqueIdentifier,
                            r -> r,
                            (existing, replacement) -> existing, // Keep first occurrence
                            java.util.LinkedHashMap::new // Maintain order
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
    }

    /**
     * Creates a summary string for logging purposes.
     *
     * @param recipients the recipient list (must not be null)
     * @return summary string with counts and types
     */
    public static String createSummary(@NotNull List<NotificationRecipient> recipients) {
        long internalCount = recipients.stream().filter(NotificationRecipient::isInternal).count();
        long externalCount = recipients.size() - internalCount;
        
        return String.format("Recipients[total=%d, internal=%d, external=%d]", 
                           recipients.size(), internalCount, externalCount);
    }
}
