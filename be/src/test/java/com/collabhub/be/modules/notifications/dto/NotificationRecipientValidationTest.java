package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that the validation issue with ExternalUserRecipient is resolved.
 * 
 * This test specifically addresses the issue where <PERSON>ber<PERSON> Val<PERSON><PERSON> was trying to
 * validate the getUserId() method on ExternalUserRecipient objects, causing an
 * UnsupportedOperationException during chat mention notification processing.
 */
class NotificationRecipientValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("ExternalUserRecipient should validate successfully without calling getUserId()")
    void testExternalUserRecipientValidation() {
        // Given: A valid external user recipient
        ExternalUserRecipient externalRecipient = ExternalUserRecipient.of(
            "<EMAIL>", 
            "Jane Smith"
        );

        // When: Validating the external recipient
        Set<ConstraintViolation<ExternalUserRecipient>> violations = validator.validate(externalRecipient);

        // Then: No validation violations should occur
        assertTrue(violations.isEmpty(), 
            "ExternalUserRecipient should validate without errors, but got: " + violations);
    }

    @Test
    @DisplayName("InternalUserRecipient should validate successfully with getUserId()")
    void testInternalUserRecipientValidation() {
        // Given: A valid internal user recipient
        InternalUserRecipient internalRecipient = InternalUserRecipient.of(
            123L, 
            "<EMAIL>", 
            "John Doe"
        );

        // When: Validating the internal recipient
        Set<ConstraintViolation<InternalUserRecipient>> violations = validator.validate(internalRecipient);

        // Then: No validation violations should occur
        assertTrue(violations.isEmpty(), 
            "InternalUserRecipient should validate without errors, but got: " + violations);
    }

    @Test
    @DisplayName("Mixed recipient list should validate successfully")
    void testMixedRecipientListValidation() {
        // Given: A mixed list of internal and external recipients
        List<NotificationRecipient> recipients = List.of(
            InternalUserRecipient.of(123L, "<EMAIL>", "John Doe"),
            ExternalUserRecipient.of("<EMAIL>", "Jane Smith"),
            InternalUserRecipient.of(456L, "<EMAIL>", "Alice Johnson"),
            ExternalUserRecipient.of("<EMAIL>", "Bob Wilson")
        );

        // When: Validating each recipient in the list
        // This simulates what happens during ChatMentionNotification validation
        assertDoesNotThrow(() -> {
            for (NotificationRecipient recipient : recipients) {
                Set<ConstraintViolation<NotificationRecipient>> violations = validator.validate(recipient);
                assertTrue(violations.isEmpty(), 
                    "Recipient " + recipient.getClass().getSimpleName() + " should validate without errors, but got: " + violations);
            }
        }, "Mixed recipient list validation should not throw any exceptions");
    }

    @Test
    @DisplayName("ExternalUserRecipient getUserId() should throw UnsupportedOperationException")
    void testExternalUserRecipientGetUserIdThrowsException() {
        // Given: A valid external user recipient
        ExternalUserRecipient externalRecipient = ExternalUserRecipient.of(
            "<EMAIL>", 
            "Jane Smith"
        );

        // When & Then: Calling getUserId() should throw UnsupportedOperationException
        assertThrows(UnsupportedOperationException.class, 
            externalRecipient::userId,
            "ExternalUserRecipient.getUserId() should throw UnsupportedOperationException");
    }

    @Test
    @DisplayName("InternalUserRecipient getUserId() should return valid user ID")
    void testInternalUserRecipientGetUserIdReturnsValidId() {
        // Given: A valid internal user recipient
        Long expectedUserId = 123L;
        InternalUserRecipient internalRecipient = InternalUserRecipient.of(
            expectedUserId, 
            "<EMAIL>", 
            "John Doe"
        );

        // When: Calling getUserId()
        Long actualUserId = internalRecipient.userId();

        // Then: Should return the expected user ID
        assertEquals(expectedUserId, actualUserId, 
            "InternalUserRecipient.getUserId() should return the correct user ID");
    }

    @Test
    @DisplayName("ChatMentionNotification with mixed recipients should validate successfully")
    void testChatMentionNotificationValidation() {
        // Given: A ChatMentionNotification with mixed recipients
        List<NotificationRecipient> recipients = List.of(
            InternalUserRecipient.of(123L, "<EMAIL>", "John Doe"),
            ExternalUserRecipient.of("<EMAIL>", "Jane Smith")
        );

        // When: Creating and validating a ChatMentionNotification
        assertDoesNotThrow(() -> {
            ChatMentionNotification notification = ChatMentionNotification.builder()
                .recipients(recipients)
                .mentionerName("Alice Johnson")
                .channelName("General Discussion")
                .messagePreview("Hey everyone, what do you think about this?")
                .entityIds(1L, 2L)
                .build();

            // This should not throw any validation exceptions
            notification.validate();
        }, "ChatMentionNotification with mixed recipients should validate without exceptions");
    }
}
